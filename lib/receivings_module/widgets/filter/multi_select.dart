import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/widgets/filter/heading.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MultiSelectFilter<T extends FilterCubit> extends StatelessWidget {
  final MultiSelectFilterModel filter;
  final VoidCallback? onTap;
  final VoidCallback? onTapCallbackNavigate;

  const MultiSelectFilter({
    Key? key,
    required this.filter,
    this.onTap,
    this.onTapCallbackNavigate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<T, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.label] !=
            previousState.values[filter.label];
      },
      builder: (context, state) {
        return _MultiSelectFilterText(
          filter: filter,
          state: state,
          onTap: onTapCallbackNavigate,
        );
      },
    );
  }
}

class _MultiSelectFilterText extends StatelessWidget {
  final MultiSelectFilterModel filter;
  final FilterState state;
  final VoidCallback? onTap;

  const _MultiSelectFilterText({
    Key? key,
    required this.filter,
    required this.state,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentValue = state.values[filter.label];
        final selectedValuesString = currentValue?.value as String? ?? '';
        final selectedValues = selectedValuesString.isEmpty
            ? <String>{}
            : selectedValuesString.split(',').toSet();

        String displayText;
        if (selectedValues.isEmpty) {
          displayText = tr(context, filter.label);
        } else {
          final selectedLabels = filter.values
              .where((item) => selectedValues.contains(item.value))
              .map((item) => tr(context, item.label))
              .toList();
          displayText = selectedLabels.join(', ');
        }

    return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FilterHeading(
              label: tr(context, filter.label),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GestureDetector(
                onTap: onTap,
                child: FLTextField(
                  enabled: false,
                  decoration: FLTextFieldDecoration(
                    hintText: displayText,
                  ),
                ),
              ),
            ),
            const Gap(4),
            const Divider(),
          ],
        );
  }
}

class _MultiSelectFilterPage<T extends FilterCubit> extends StatelessWidget {
  const _MultiSelectFilterPage({
    Key? key,
    required BuildContext context,
    required MultiSelectFilterModel filter,
    required FilterState state,
    required FLModalBottomSheetController controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<U>();

    return BlocBuilder<U, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.label] != previousState.values[filter.label];
      },
      builder: (context, currentState) {
        final currentValue = currentState.values[filter.label];
        final selectedValuesString = currentValue?.value as String? ?? '';
        final selectedValues = selectedValuesString.isEmpty
            ? <String>{}
            : selectedValuesString.split(',').toSet();

        return Padding(
          padding: const EdgeInsets.only(
            top: FLSpacings.sm,
            bottom: FLSpacings.xlg,
          ),
          child: Column(
            children: filter.values.map((option) {
              final isSelected = selectedValues.contains(option.value);
              return ListTile(
                title: Text(tr(context, option.label)),
                trailing: Checkbox(
                  value: isSelected,
                  onChanged: (checked) {
                    final newSelectedValues = Set<String>.from(selectedValues);
                    if (checked ?? false) {
                      newSelectedValues.add(option.value);
                    } else {
                      newSelectedValues.remove(option.value);
                    }

                    final newValue = newSelectedValues.isEmpty
                        ? ''
                        : newSelectedValues.join(',');

                    cubit.add(
                      id: filter.label,
                      value: FilterValueModel(
                        label: filter.label,
                        value: newValue,
                      ),
                    );
                  },
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
